// Variables
$font-stack: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Aria<PERSON>, sans-serif;
$line-height: 1.5;
$base-spacing: 0.75rem; // 减少基础间距从 1rem 到 0.75rem
$small-spacing: 0.5rem; // 新增小间距变量
$border-radius: 6px;
$transition-speed: 0.2s;

// Light theme colors
$light-text: #333;
$light-bg: #fff;
$light-border: #eaeaea;
$light-code-bg: rgba(27, 31, 35, 0.05);
$light-link: #0366d6;
$light-link-hover: #0057a6;
$light-table-header: #f6f8fa;
$light-table-row: #fafafa;
$light-table-hover: #f3f6fb;
$light-blockquote: #f9f9f9;
$light-blockquote-border: #dfe2e5;

// Dark theme colors
$dark-text: #e6e6e6;
$dark-bg: #1e1e1e;
$dark-border: #444;
$dark-code-bg: rgba(240, 246, 252, 0.15);
$dark-link: #58a6ff;
$dark-table-header: #2d2d2d;
$dark-table-row: #2d2d2d;
$dark-table-hover: #3b3b3b;
$dark-blockquote: #272727;

// Base styles
.ai-markdown-renderer {
    font-family: $font-stack;
    line-height: $line-height;
    color: $light-text;
    padding: 8px; // 进一步减少内边距
    max-width: 100%;
    overflow-x: auto;

    // 更精细的间距控制，大幅减少通用间距
    > * + * {
        margin-top: 0.3rem; // 进一步减少通用间距
    }

    // 特定元素的间距优化
    > .markdown-paragraph + .markdown-paragraph {
        margin-top: 0.3rem;
    }

    > .markdown-heading + * {
        margin-top: 0.25rem; // 减少标题后的间距
    }

    > * + .markdown-heading {
        margin-top: 0.6rem; // 减少标题前的间距
    }

    > .code-block-container + * {
        margin-top: 0.3rem;
    }

    > * + .code-block-container {
        margin-top: 0.3rem;
    }

    > .markdown-list + * {
        margin-top: 0.3rem;
    }

    > * + .markdown-list {
        margin-top: 0.2rem; // 进一步减少列表前的间距
    }

    // 特殊情况：连续的代码块之间
    > .code-block-container + .code-block-container {
        margin-top: 0.2rem;
    }

    // 特殊情况：段落和代码块之间
    > .markdown-paragraph + .code-block-container {
        margin-top: 0.25rem;
    }

    > .code-block-container + .markdown-paragraph {
        margin-top: 0.25rem;
    }
}

.theme-switcher {
    margin-bottom: 0.3rem; // 减少主题切换器的底部间距
}

// Headings
.markdown-heading {
    margin: 0.5rem 0 0.2rem; // 进一步减少标题的上下间距
    font-weight: 700;
    line-height: 1.2; // 减少行高
    border-bottom: 1px solid $light-border;
    padding-bottom: 0.15em; // 进一步减少下内边距
    scroll-margin-top: 90px;

    &-1 {
        font-size: 2em;
    }
    &-2 {
        font-size: 1.5em;
    }
    &-3 {
        font-size: 1.25em;
    }
    &-4 {
        font-size: 1em;
    }
    &-5 {
        font-size: 0.875em;
    }
    &-6 {
        font-size: 0.85em;
        color: #6a737d;
    }
}

// Paragraphs
.markdown-paragraph {
    margin: 0 0 $small-spacing 0; // 减少段落底部间距
}

// Links
.markdown-link {
    color: $light-link;
    text-decoration: none;
    position: relative;
    transition: color $transition-speed ease-in-out;

    &:hover {
        text-decoration: underline;
        color: $light-link-hover;
    }

    &::after {
        content: "";
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 0%;
        height: 1px;
        background-color: currentColor;
        transition: width 0.3s ease-in-out;
    }

    &:hover::after {
        width: 100%;
    }
}

// Lists
.markdown-list {
    padding-left: 1.5em;
    margin: 0 0 0.3rem 0; // 进一步减少列表底部间距

    &-item {
        margin-bottom: 0.05rem; // 进一步减少列表项之间的间距
        line-height: 1.3; // 调整行高更紧凑

        // 嵌套列表的特殊处理
        .markdown-list {
            margin-top: 0.05rem;
            margin-bottom: 0.05rem;
        }
    }

    &-ul {
        list-style-type: disc;
    }
    &-ol {
        list-style-type: decimal;
    }
}

// Code blocks
.code-block-container {
    position: relative;
    margin: 0.3rem 0; // 进一步减少代码块的上下间距
    border-radius: $border-radius;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    border: 1px solid $light-border;
}

.code-block-filepath,
.code-block-header {
    background-color: #2d2d2d;
    padding: 4px 12px; // 减少代码块头部的内边距
    color: $dark-text;
    font-family: monospace;
    font-size: 0.85em;
    border-bottom: 1px solid $dark-border;
}

.code-language-label {
    text-transform: uppercase;
    font-size: 0.75em;
    font-weight: bold;
    letter-spacing: 0.5px;
}

.copy-button {
    background: transparent;
    border: none;
    color: $dark-text;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color $transition-speed ease-in-out;
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.8em;

    &:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }
}

.inline-code {
    background-color: $light-code-bg;
    border-radius: 3px;
    font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
    font-size: 0.85em;
    padding: 0.2em 0.4em;
    vertical-align: middle;
}

// Tables
.table-container {
    overflow-x: auto;
    margin-bottom: $small-spacing; // 减少表格底部间距
}

.markdown-table {
    border-collapse: collapse;
    width: 100%;
    margin: 0 0 $small-spacing 0; // 减少表格底部间距
    background-color: $light-bg;
    border-radius: $border-radius;
    overflow: hidden;

    th,
    td {
        padding: 8px 12px;
        border: 1px solid $light-border;
    }

    th {
        background-color: $light-table-header;
        font-weight: 600;
    }

    tr {
        &:nth-child(even) {
            background-color: $light-table-row;
        }

        &:hover {
            background-color: $light-table-hover;
        }
    }
}

// Blockquotes
.markdown-blockquote {
    margin: 0 0 $small-spacing; // 减少引用块底部间距
    padding: 0.5em 0.8em; // 减少引用块内边距
    color: #6a737d;
    border-left: 4px solid $light-blockquote-border;
    background-color: $light-blockquote;
    border-radius: 4px;
}

// Images
.markdown-image {
    max-width: 100%;
    box-sizing: border-box;
    margin: $small-spacing 0; // 减少图片上下间距
    border-radius: 4px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

// Dark mode
@media (prefers-color-scheme: dark) {
    .ai-markdown-renderer {
        color: $dark-text;
        background-color: $dark-bg;
    }

    .markdown-heading {
        border-bottom-color: $dark-border;
    }

    .markdown-link {
        color: $dark-link;
    }

    .inline-code {
        background-color: $dark-code-bg;
        color: $dark-text;
    }

    .markdown-table {
        background-color: $dark-bg;

        th,
        td {
            border-color: $dark-border;
        }

        th {
            background-color: $dark-table-header;
        }

        tr {
            &:nth-child(even) {
                background-color: $dark-table-row;
            }

            &:hover {
                background-color: $dark-table-hover;
            }
        }
    }

    .markdown-blockquote {
        color: #8b949e;
        border-left-color: $dark-border;
        background-color: $dark-blockquote;
    }
}
