// Variables
$font-stack: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
$line-height: 1.6;
$base-spacing: 1rem;
$border-radius: 6px;
$transition-speed: 0.2s;

// Light theme colors
$light-text: #333;
$light-bg: #fff;
$light-border: #eaeaea;
$light-code-bg: rgba(27, 31, 35, 0.05);
$light-link: #0366d6;
$light-link-hover: #0057a6;
$light-table-header: #f6f8fa;
$light-table-row: #fafafa;
$light-table-hover: #f3f6fb;
$light-blockquote: #f9f9f9;
$light-blockquote-border: #dfe2e5;

// Dark theme colors
$dark-text: #e6e6e6;
$dark-bg: #1e1e1e;
$dark-border: #444;
$dark-code-bg: rgba(240, 246, 252, 0.15);
$dark-link: #58a6ff;
$dark-table-header: #2d2d2d;
$dark-table-row: #2d2d2d;
$dark-table-hover: #3b3b3b;
$dark-blockquote: #272727;

// Base styles
.ai-markdown-renderer {
    font-family: $font-stack;
    line-height: $line-height;
    color: $light-text;
    padding: 16px;
    max-width: 100%;
    overflow-x: auto;

    > * + * {
        margin-top: $base-spacing;
    }

    // 减少列表的顶部间距
    > .markdown-list {
        margin-top: 0.5rem;
    }

    > * + .markdown-list {
        margin-top: 0.5rem;
    }
}

.theme-switcher {
    margin-bottom: $base-spacing;
}

// Headings
.markdown-heading {
    margin: 1.5rem 0 0.5rem;
    font-weight: 700;
    line-height: 1.25;
    border-bottom: 1px solid $light-border;
    padding-bottom: 0.3em;
    scroll-margin-top: 90px;

    &-1 {
        font-size: 2em;
    }
    &-2 {
        font-size: 1.5em;
    }
    &-3 {
        font-size: 1.25em;
    }
    &-4 {
        font-size: 1em;
    }
    &-5 {
        font-size: 0.875em;
    }
    &-6 {
        font-size: 0.85em;
        color: #6a737d;
    }
}

// Paragraphs
.markdown-paragraph {
    margin: 0 0 $base-spacing 0;
}

// Links
.markdown-link {
    color: $light-link;
    text-decoration: none;
    position: relative;
    transition: color $transition-speed ease-in-out;

    &:hover {
        text-decoration: underline;
        color: $light-link-hover;
    }

    &::after {
        content: "";
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 0%;
        height: 1px;
        background-color: currentColor;
        transition: width 0.3s ease-in-out;
    }

    &:hover::after {
        width: 100%;
    }
}

// Lists
.markdown-list {
    padding-left: 1.5em;
    margin: 0 0 0.5rem 0; // 减少列表底部间距

    &-item {
        margin-bottom: 0.1rem; // 减少列表项之间的间距
        line-height: 1.4; // 调整行高

        // 嵌套列表的特殊处理
        .markdown-list {
            margin-top: 0.1rem;
            margin-bottom: 0.1rem;
        }
    }

    &-ul {
        list-style-type: disc;
    }
    &-ol {
        list-style-type: decimal;
    }
}

// Code blocks
.code-block-container {
    position: relative;
    margin: $base-spacing 0;
    border-radius: $border-radius;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    border: 1px solid $light-border;
}

.code-block-filepath,
.code-block-header {
    background-color: #2d2d2d;
    padding: 6px 16px;
    color: $dark-text;
    font-family: monospace;
    font-size: 0.85em;
    border-bottom: 1px solid $dark-border;
}

.code-language-label {
    text-transform: uppercase;
    font-size: 0.75em;
    font-weight: bold;
    letter-spacing: 0.5px;
}

.copy-button {
    background: transparent;
    border: none;
    color: $dark-text;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color $transition-speed ease-in-out;
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.8em;

    &:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }
}

.inline-code {
    background-color: $light-code-bg;
    border-radius: 3px;
    font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
    font-size: 0.85em;
    padding: 0.2em 0.4em;
    vertical-align: middle;
}

// Tables
.table-container {
    overflow-x: auto;
    margin-bottom: $base-spacing;
}

.markdown-table {
    border-collapse: collapse;
    width: 100%;
    margin: 0 0 $base-spacing 0;
    background-color: $light-bg;
    border-radius: $border-radius;
    overflow: hidden;

    th,
    td {
        padding: 8px 12px;
        border: 1px solid $light-border;
    }

    th {
        background-color: $light-table-header;
        font-weight: 600;
    }

    tr {
        &:nth-child(even) {
            background-color: $light-table-row;
        }

        &:hover {
            background-color: $light-table-hover;
        }
    }
}

// Blockquotes
.markdown-blockquote {
    margin: 0 0 $base-spacing;
    padding: 0.75em 1em;
    color: #6a737d;
    border-left: 4px solid $light-blockquote-border;
    background-color: $light-blockquote;
    border-radius: 4px;
}

// Images
.markdown-image {
    max-width: 100%;
    box-sizing: border-box;
    margin: $base-spacing 0;
    border-radius: 4px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

// Dark mode
@media (prefers-color-scheme: dark) {
    .ai-markdown-renderer {
        color: $dark-text;
        background-color: $dark-bg;
    }

    .markdown-heading {
        border-bottom-color: $dark-border;
    }

    .markdown-link {
        color: $dark-link;
    }

    .inline-code {
        background-color: $dark-code-bg;
        color: $dark-text;
    }

    .markdown-table {
        background-color: $dark-bg;

        th,
        td {
            border-color: $dark-border;
        }

        th {
            background-color: $dark-table-header;
        }

        tr {
            &:nth-child(even) {
                background-color: $dark-table-row;
            }

            &:hover {
                background-color: $dark-table-hover;
            }
        }
    }

    .markdown-blockquote {
        color: #8b949e;
        border-left-color: $dark-border;
        background-color: $dark-blockquote;
    }
}
