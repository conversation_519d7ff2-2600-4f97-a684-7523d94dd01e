// Polyfill for Node.js process module in browser environment

/**
 * Browser-compatible process object
 */
const process = {
  // Current working directory - in browser context, use root
  cwd() {
    return '/'
  },
  
  // Process environment variables
  env: {
    NODE_ENV: 'production'
  },
  
  // Platform information
  platform: 'browser',
  
  // Process version
  version: 'v18.0.0',
  
  // Process versions
  versions: {
    node: '18.0.0'
  },
  
  // Process argv
  argv: [],
  
  // Process exit
  exit(code = 0) {
    // In browser, we can't actually exit, so just log
    console.warn('process.exit() called with code:', code)
  },
  
  // Process nextTick
  nextTick(callback, ...args) {
    setTimeout(() => callback(...args), 0)
  },
  
  // Process stdout/stderr
  stdout: {
    write(data) {
      console.log(data)
    }
  },
  
  stderr: {
    write(data) {
      console.error(data)
    }
  }
}

export default process
export { process }

// Export as minproc for vfile compatibility
export const minproc = process

// For compatibility with different import styles
export const cwd = process.cwd
export const env = process.env
export const platform = process.platform
export const version = process.version
export const versions = process.versions
export const argv = process.argv
export const exit = process.exit
export const nextTick = process.nextTick
export const stdout = process.stdout
export const stderr = process.stderr
